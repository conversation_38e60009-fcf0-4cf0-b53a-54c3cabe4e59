<template>
  <div class="device-control-record">
    <div class="record-list">
      <div v-if="displayRecordList.length > 0" class="record-scroll">
        <vue-seamless-scroll :data="displayRecordList" :class-option="classOption" class="seamless-warp" style="width: 100%">
          <div>
            <div 
              class="record-item" 
              v-for="(item, index) in displayRecordList" 
              :key="index"
              :class="{ 'alternate-bg': (index + 1) % 2 === 0 && index > 0 }"
            >
              <div class="device-name">{{ item.deviceName }}</div>
              <div class="control-value">{{ formatControlValue(item.controlValue) }}</div>
              <div class="control-time">{{ item.controlTime }}</div>
            </div>
          </div>
        </vue-seamless-scroll>
      </div>
      <div v-if="!displayRecordList || displayRecordList.length === 0" class="no-data">
        暂无设备控制记录
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeviceControlRecord',
  props: {
    recordData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 无缝滚动配置
      classOption: {
        step: 0.5, // 步长
        limitMoveNum: 2, // 限制滚动数量
        hoverStop: true, // 是否启用鼠标hover控制
        direction: 1, // 1向上，0向下
        openWatch: true, // 开启数据监听
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动)
        waitTime: 1000, // 单步停止等待时间(ms)
      }
    }
  },
  computed: {
    // 显示设备控制记录列表
    displayRecordList() {
      if (this.recordData && Array.isArray(this.recordData.recordList)) {
        return this.recordData.recordList;
      }
      return [];
    }
  },
  methods: {
    // 格式化控制值，将包含 true/false 的字符串中的布尔值转换为开/关
    formatControlValue(value) {
      // 如果是字符串且包含 true/false，则替换其中的布尔值
      if (typeof value === 'string') {
        return value
          .replace(/\btrue\b/g, '开')    // 替换独立的 true 单词
          .replace(/\bfalse\b/g, '关');  // 替换独立的 false 单词
      }
      
      // 如果不是字符串或布尔值，则直接返回原值
      return value;
    }
  },
  mounted() {
    // 组件挂载完成后的逻辑
  },
  beforeDestroy() {
    // 组件销毁前的清理逻辑
    const that = this;
    Object.assign(that.$data, that.$options.data());
  }
}
</script>

<style lang="less" scoped>
.device-control-record {
  width: 100%;
  height: 86%;
  padding: 25px 0 10px 0;

  .record-list {
    width: 100%;
    height: 100%;
    overflow-y: hidden; // 改为hidden，由无缝滚动组件控制
    position: relative;

    .record-scroll {
      width: 100%;
      height: 100%;
    }

    .record-item {
      position: relative;
      height: 32px;
      padding: 0 15px;
      font-size: 14px;
      color: #DFEEF3;
      line-height: 32px;
      font-style: MicrosoftYaHei;

      &.alternate-bg {
        background-color: rgba(0, 212, 255, 0.06);
      }

      .device-name {
        position: absolute;
        left: 15px;
        top: 0;
        width: 129px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .control-value {
        position: absolute;
        left: 160px; 
        top: 0;
        width: 68px;
        white-space: nowrap;
      }

      .control-time {
        position: absolute;
        left: 320px; 
        top: 0;
        white-space: nowrap;
      }
    }
    
    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: rgba(223, 238, 243, 0.6);
      font-size: 14px;
    }
  }

  /* 无需滚动条样式，由组件控制 */
}

/* 无缝滚动组件样式覆盖 */
.seamless-warp {
  height: 100%;
  overflow: hidden;
}

</style>
